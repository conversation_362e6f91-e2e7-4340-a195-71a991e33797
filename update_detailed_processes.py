#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量更新施工避坑指南H5内容 - 添加详细施工流程
"""

import os
import re

# 定义各期的详细流程内容
detailed_processes = {
    6: {
        "title": "非承重墙拆除详细流程",
        "steps": [
            {
                "title": "拆除前准备工作（30分钟）",
                "content": """<p><strong>工具准备：</strong>电锤、切割机、撬棍、安全锤、管线探测仪、防护用品</p>
                <p><strong>区域保护：</strong>铺设防尘膜，保护家具和地面，设置隔离区</p>
                <p><strong>管线探测：</strong>用探测仪确认墙内无水电管线，标记安全区域</p>
                <p><strong>安全检查：</strong>确认非承重墙性质，检查周边结构安全</p>"""
            },
            {
                "title": "分段标记切割（20分钟）",
                "content": """<p><strong>切割标记：</strong>按1m×1m分段标记，避免大块倒塌</p>
                <p><strong>切割顺序：</strong>从上到下，从边缘到中心，先横后竖</p>
                <p><strong>切割深度：</strong>控制切割深度，避免损伤相邻墙体</p>
                <p><strong>降尘措施：</strong>湿法切割，配备吸尘设备</p>"""
            },
            {
                "title": "逐块拆除清理（60分钟）",
                "content": """<p><strong>拆除方法：</strong>用撬棍和安全锤逐块拆除，控制倒塌方向</p>
                <p><strong>垃圾清理：</strong>及时清理拆除垃圾，保持通道畅通</p>
                <p><strong>边缘处理：</strong>清理墙体边缘，修整不平整部位</p>
                <p><strong>安全检查：</strong>检查相邻墙体是否受损，及时处理</p>"""
            }
        ]
    },
    7: {
        "title": "门窗拆改详细流程",
        "steps": [
            {
                "title": "测量记录准备（20分钟）",
                "content": """<p><strong>尺寸测量：</strong>精确测量门窗洞口尺寸，记录偏差情况</p>
                <p><strong>拍照记录：</strong>拍摄原有门窗状态，记录五金件位置</p>
                <p><strong>材料确认：</strong>确认新门窗规格，核对安装要求</p>
                <p><strong>工具准备：</strong>准备拆卸工具、测量工具、保护材料</p>"""
            },
            {
                "title": "五金件拆卸（30分钟）",
                "content": """<p><strong>拆卸顺序：</strong>先拆把手锁具，再拆合页滑轨，最后拆框体</p>
                <p><strong>螺丝保存：</strong>分类保存螺丝和五金件，便于重复使用</p>
                <p><strong>标记位置：</strong>标记五金件安装位置，便于新门窗安装</p>
                <p><strong>检查磨损：</strong>检查五金件磨损情况，确定是否需要更换</p>"""
            },
            {
                "title": "框体拆除安装（90分钟）",
                "content": """<p><strong>切断固定：</strong>切断膨胀螺栓和焊接点，小心取出框体</p>
                <p><strong>洞口修整：</strong>清理洞口，修补破损部位，确保平整</p>
                <p><strong>新框安装：</strong>按规范安装新框体，调整水平垂直度</p>
                <p><strong>密封处理：</strong>打胶密封，确保防水防风效果</p>"""
            }
        ]
    },
    # 继续添加其他期数的详细流程...
}

def update_episode_process(episode_num, process_data):
    """更新单期的详细流程"""
    filename = f"episodes/episode-{episode_num:02d}.html"
    
    if not os.path.exists(filename):
        print(f"文件不存在：{filename}")
        return False
    
    # 读取原文件
    with open(filename, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 构建新的详细流程HTML
    new_process_html = f'''            <section class="content-section">
                <h2>🔧 {process_data["title"]}</h2>
                <div class="process-flow">
                    <div class="flow-steps">'''
    
    for i, step in enumerate(process_data["steps"], 1):
        new_process_html += f'''
                        <div class="flow-step">
                            <div class="step-number">{i}</div>
                            <div class="step-content">
                                <h4>{step["title"]}</h4>
                                {step["content"]}
                            </div>
                        </div>'''
    
    new_process_html += '''
                    </div>
                </div>
            </section>'''
    
    # 查找并替换原有的流程部分
    pattern = r'<section class="content-section">\s*<h2>🔧[^<]*</h2>.*?</section>'
    
    if re.search(pattern, content, re.DOTALL):
        # 替换现有的流程部分
        new_content = re.sub(pattern, new_process_html, content, count=1, flags=re.DOTALL)
    else:
        print(f"未找到流程部分：{filename}")
        return False
    
    # 写入更新后的内容
    with open(filename, 'w', encoding='utf-8') as f:
        f.write(new_content)
    
    print(f"第{episode_num}期流程更新完成")
    return True

def main():
    """主函数：批量更新详细流程"""
    print("开始批量更新详细施工流程...")
    
    success_count = 0
    total_count = len(detailed_processes)
    
    for episode_num, process_data in detailed_processes.items():
        if update_episode_process(episode_num, process_data):
            success_count += 1
    
    print(f"更新完成：{success_count}/{total_count} 期")
    print("注意：由于内容量大，建议分批次手动完善剩余期数的详细流程")

if __name__ == "__main__":
    main()
