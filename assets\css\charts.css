/* 图表和可视化元素样式 */

/* 流程图样式 */
.process-flow {
    background: white;
    border-radius: 12px;
    padding: 24px;
    margin: 20px 0;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.flow-steps {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.flow-step {
    display: flex;
    align-items: center;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #667eea;
    position: relative;
}

.flow-step:not(:last-child)::after {
    content: '↓';
    position: absolute;
    bottom: -24px;
    left: 50%;
    transform: translateX(-50%);
    color: #667eea;
    font-size: 18px;
    font-weight: bold;
}

.step-number {
    background: #667eea;
    color: white;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    margin-right: 16px;
    flex-shrink: 0;
}

.step-content h4 {
    font-size: 16px;
    margin-bottom: 4px;
    color: #2c3e50;
}

.step-content p {
    font-size: 14px;
    color: #666;
    margin: 0;
}

/* 对比表格样式 */
.comparison-table {
    background: white;
    border-radius: 12px;
    padding: 24px;
    margin: 20px 0;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    overflow-x: auto;
}

.comparison-table table {
    width: 100%;
    border-collapse: collapse;
}

.comparison-table th,
.comparison-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.comparison-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #2c3e50;
}

.comparison-table .correct {
    color: #27ae60;
}

.comparison-table .incorrect {
    color: #e74c3c;
}

.comparison-table .neutral {
    color: #666;
}

/* 检查清单样式 */
.checklist {
    background: white;
    border-radius: 12px;
    padding: 24px;
    margin: 20px 0;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.checklist h3 {
    font-size: 18px;
    margin-bottom: 16px;
    color: #2c3e50;
}

.checklist-item {
    display: flex;
    align-items: flex-start;
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;
}

.checklist-item:last-child {
    border-bottom: none;
}

.checklist-checkbox {
    width: 20px;
    height: 20px;
    border: 2px solid #ddd;
    border-radius: 4px;
    margin-right: 12px;
    margin-top: 2px;
    flex-shrink: 0;
    cursor: pointer;
    transition: all 0.3s ease;
}

.checklist-checkbox.checked {
    background: #27ae60;
    border-color: #27ae60;
    position: relative;
}

.checklist-checkbox.checked::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.checklist-text {
    flex: 1;
    font-size: 14px;
    color: #333;
    line-height: 1.5;
}

/* 警告和提示框样式 */
.alert-box {
    padding: 16px;
    border-radius: 8px;
    margin: 16px 0;
    display: flex;
    align-items: flex-start;
    gap: 12px;
}

.alert-box.warning {
    background: #fff3cd;
    border-left: 4px solid #ffc107;
    color: #856404;
}

.alert-box.danger {
    background: #f8d7da;
    border-left: 4px solid #dc3545;
    color: #721c24;
}

.alert-box.info {
    background: #d1ecf1;
    border-left: 4px solid #17a2b8;
    color: #0c5460;
}

.alert-box.success {
    background: #d4edda;
    border-left: 4px solid #28a745;
    color: #155724;
}

.alert-icon {
    font-size: 20px;
    margin-top: 2px;
}

.alert-content {
    flex: 1;
}

.alert-content h4 {
    font-size: 16px;
    margin-bottom: 8px;
    font-weight: 600;
}

.alert-content p {
    font-size: 14px;
    margin: 0;
    line-height: 1.5;
}

/* 进度条样式 */
.progress-bar {
    background: #f0f0f0;
    border-radius: 10px;
    height: 20px;
    margin: 16px 0;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea, #764ba2);
    border-radius: 10px;
    transition: width 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 12px;
    font-weight: 600;
}

/* 标签和徽章样式 */
.tag {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    margin: 2px;
}

.tag.primary {
    background: #e3f2fd;
    color: #1976d2;
}

.tag.success {
    background: #e8f5e8;
    color: #2e7d32;
}

.tag.warning {
    background: #fff3e0;
    color: #f57c00;
}

.tag.danger {
    background: #ffebee;
    color: #c62828;
}

/* 响应式图表 */
@media (max-width: 768px) {
    .flow-steps {
        gap: 12px;
    }
    
    .flow-step {
        padding: 12px;
    }
    
    .flow-step:not(:last-child)::after {
        bottom: -20px;
        font-size: 16px;
    }
    
    .step-number {
        width: 28px;
        height: 28px;
        margin-right: 12px;
    }
    
    .comparison-table {
        padding: 16px;
    }
    
    .comparison-table th,
    .comparison-table td {
        padding: 8px;
        font-size: 14px;
    }
    
    .checklist {
        padding: 16px;
    }
    
    .alert-box {
        padding: 12px;
        gap: 8px;
    }
}

@media (max-width: 480px) {
    .flow-step {
        flex-direction: column;
        text-align: center;
        gap: 8px;
    }
    
    .step-number {
        margin-right: 0;
        margin-bottom: 8px;
    }
    
    .comparison-table {
        font-size: 12px;
    }
    
    .comparison-table th,
    .comparison-table td {
        padding: 6px;
    }
}
